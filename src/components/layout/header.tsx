import { SidebarTrigger } from '@/components/ui/sidebar';
import { But<PERSON> } from '@/components/ui/button';
import { UserCircle, Bell, Newspaper, Activity, TrendingUp, AlertCircle } from 'lucide-react';
import { Logo } from '@/components/icons/logo';
import Link from 'next/link';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { NotificationList } from './notification-list'; // New component for notifications

export function Header() {
  return (
    <header className="sticky top-0 z-30 flex h-16 items-center justify-between gap-4 border-b bg-background/80 px-4 shadow-sm backdrop-blur-md md:px-6">
      <div className="flex items-center gap-2">
        <SidebarTrigger className="md:hidden" />
        <Link href="/" className="hidden md:block">
           <Logo className="h-8 w-auto text-primary" />
        </Link>
      </div>
      <div className="flex items-center gap-3">
        <ThemeToggle />
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="ghost" size="icon" aria-label="Notifications">
              <Bell className="h-5 w-5" />
              {/* Future: Add a badge here for unread notification count */}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80 p-0" align="end">
            <NotificationList />
          </PopoverContent>
        </Popover>
        <Button variant="ghost" size="icon" aria-label="User Profile">
          <UserCircle className="h-6 w-6" />
        </Button>
      </div>
    </header>
  );
}
