'use client';

import { useState, useEffect } from 'react';
import { Activity } from 'lucide-react';

// Helper function to get clean display names for chart tabs
function getChartTabLabel(instrument: string): string {
  switch (instrument) {
    case 'Volatility 10 Index': return 'V10';
    case 'Volatility 25 Index': return 'V25';
    case 'Volatility 50 Index': return 'V50';
    case 'Volatility 75 Index': return 'V75';
    case 'Volatility 100 Index': return 'V100';
    case 'Volatility 10 (1s) Index': return 'V10 (1s)';
    case 'Volatility 25 (1s) Index': return 'V25 (1s)';
    case 'Volatility 50 (1s) Index': return 'V50 (1s)';
    case 'Volatility 75 (1s) Index': return 'V75 (1s)';
    case 'Volatility 100 (1s) Index': return 'V100 (1s)';
    default: return instrument;
  }
}

import { DerivBalanceListener, type ListenerStatus } from '@/services/deriv-balance-listener';

const DEFAULT_PAPER_BALANCE = 10000;
const DEFAULT_LIVE_BALANCE = 0;

// Map Deriv API contract statuses to local trade statuses
const mapDerivStatusToLocal = (derivStatus?: string): ActiveAutomatedVolatilityTrade['status'] => {
  if (!derivStatus) return 'pending_execution';
  switch (derivStatus) {
    case 'open': return 'pending_execution';
    case 'sold': return 'closed_manual';
    case 'won': return 'won';
    case 'lost': return 'lost_duration';
    case 'cancelled': return 'failed_placement';
    default:
      console.warn(`[VolatilityPage] Unknown Deriv contract status: ${derivStatus}`);
      return 'pending_execution';
  }
};

export default function VolatilityTradingClient() {
  const router = useRouter();
  const {
    authStatus,
    userInfo,
    selectedDerivAccountType,
    derivDemoBalance,
    derivLiveBalance,
    derivDemoAccountId,
    derivRealAccountId,
    updateSelectedDerivAccountType,
  } = useAuth();

  const [currentVolatilityInstrument, setCurrentVolatilityInstrument] = useState<VolatilityInstrumentType>('Volatility 100 Index');

  // New state variables for the updated controls
  const [executionMode, setExecutionMode] = useState<'turbo' | 'safe'>('safe');
  const [numberOfBulkTrades, setNumberOfBulkTrades] = useState<number>(1);

  // Manual execution mode toggle
  const [isManualMode, setIsManualMode] = useState<boolean>(false);

  // Pattern monitoring state
  const [isPatternMonitoring, setIsPatternMonitoring] = useState<boolean>(false);
  const [patternMonitoringStatus, setPatternMonitoringStatus] = useState<string>('');
  const [patternMonitoringProgress, setPatternMonitoringProgress] = useState<{
    consecutiveCount: number;
    currentDigit: number;
    needed: number;
  } | null>(null);

  // State for Over/Under digit selection
  const [selectedOverDigit, setSelectedOverDigit] = useState<number | null>(null);
  const [selectedUnderDigit, setSelectedUnderDigit] = useState<number | null>(null);

  // State for prediction digit input (for Over/Under trade type)
  const [predictionDigit, setPredictionDigit] = useState<number | null>(null);
  const [predictionDigitError, setPredictionDigitError] = useState<string>('');

  // State for strategy selection
  const [selectedStrategy, setSelectedStrategy] = useState<string>('');

  // State for pattern analysis
  const [patternAnalysis, setPatternAnalysis] = useState({
    evenContinuous: 0,
    oddContinuous: 0,
    evenReversals: 0,
    oddReversals: 0,
    lastAnalyzedLength: 0
  });

  // State for real-time price streaming for trade type cards
  const [currentStreamingPrice, setCurrentStreamingPrice] = useState<number>(0);
  const [priceSequence, setPriceSequence] = useState<Array<{price: number, digit: number, timestamp: number}>>([]);

  // Handler for digit selection that allows one from each column
  const handleDigitSelection = useCallback((digitValue: number, digitType: 'over' | 'under') => {
    if (digitType === 'over') {
      // If the same Over digit is clicked again, toggle it off
      if (selectedOverDigit === digitValue) {
        setSelectedOverDigit(null);
      } else {
        // Otherwise select this digit for Over
        setSelectedOverDigit(digitValue);
      }
    } else { // under
      // If the same Under digit is clicked again, toggle it off
      if (selectedUnderDigit === digitValue) {
        setSelectedUnderDigit(null);
      } else {
        // Otherwise select this digit for Under
        setSelectedUnderDigit(digitValue);
      }
    }
  }, [selectedOverDigit, selectedUnderDigit]);

  // Cancel pattern monitoring function
  const handleCancelPatternMonitoring = useCallback(() => {
    console.log('[VolatilityPage] User requested pattern monitoring cancellation');

    // Local cancellation - just reset the UI state
    setIsPatternMonitoring(false);
    setPatternMonitoringStatus('');
    setPatternMonitoringProgress(null);
    setIsAiLoading(false);
    setIsAutoTradingActive(false);

    toast({
      title: "Pattern Monitoring Cancelled",
      description: "Pattern monitoring has been stopped.",
    });
  }, []);

  // Handler for prediction digit input validation
  const handlePredictionDigitChange = useCallback((value: string) => {
    setPredictionDigitError('');

    if (value === '') {
      setPredictionDigit(null);
      return;
    }

    // Validate single digit (0-9)
    if (!/^\d$/.test(value)) {
      setPredictionDigitError('Please enter a single digit (0-9)');
      return;
    }

    const digit = parseInt(value);
    setPredictionDigit(digit);
  }, []);

  // Legacy state variables (keeping for backward compatibility)
  const [tradingMode, setTradingMode] = useState<TradingMode>('balanced');
  const [selectedAiStrategyId, setSelectedAiStrategyId] = useState<string>(DEFAULT_AI_STRATEGY_ID);

  const [selectedUserTradeTypeForLoop, setSelectedUserTradeTypeForLoop] = useState<UserTradeTypeValue>('DigitsEvenOdd');

  const [autoTradeTotalStake, setAutoTradeTotalStake] = useState<number>(10);
  const [isAutoTradingActive, setIsAutoTradingActive] = useState(false);
  const [activeAutomatedTrades, setActiveAutomatedTrades] = useState<ActiveAutomatedVolatilityTrade[]>([]);
  const [profitsClaimable, setProfitsClaimable] = useState<ProfitsClaimable>({
    totalNetProfit: 0,
    tradeCount: 0,
    winningTrades: 0,
    losingTrades: 0,
  });
  const [isAiLoading, setIsAiLoading] = useState(false);
  const tradeIntervals = useRef<Map<string, NodeJS.Timeout>>(new Map());
  const realTradeMonitoringInterval = useRef<NodeJS.Timeout | null>(null);

  const [consecutiveAiCallCount, setConsecutiveAiCallCount] = useState(0);
  const [lastAiCallTimestamp, setLastAiCallTimestamp] = useState<number | null>(null);
  const AI_COOLDOWN_DURATION_MS = 2 * 60 * 1000;

  const { toast } = useToast();

  const [freshDemoBalance, setFreshDemoBalance] = useState<number | null>(null);
  const [freshRealBalance, setFreshRealBalance] = useState<number | null>(null);
  const [isLoadingDemoBalance, setIsLoadingDemoBalance] = useState<boolean>(false);
  const [isLoadingRealBalance, setIsLoadingRealBalance] = useState<boolean>(false);
  const [demoSyncStatus, setDemoSyncStatus] = useState<ListenerStatus>('idle');
  const [realSyncStatus, setRealSyncStatus] = useState<ListenerStatus>('idle');
  const demoBalanceListenerRef = useRef<DerivBalanceListener | null>(null);
  const realBalanceListenerRef = useRef<DerivBalanceListener | null>(null);

  const USER_TRADE_TYPES_OPTIONS: { value: UserTradeTypeValue; label: string }[] = [
    { value: 'RiseFall', label: 'Rise/Fall' },
    { value: 'DigitsOverUnder', label: 'Over/Under' },
    { value: 'DigitsEvenOdd', label: 'Even/Odd' },
  ];

  // Strategy options based on trade type
  const getStrategyOptions = useCallback((tradeType: UserTradeTypeValue | undefined) => {
    switch (tradeType) {
      case 'DigitsEvenOdd':
        return [
          { value: 'Even', label: 'Even' },
          { value: 'Odd', label: 'Odd' }
        ];
      case 'RiseFall':
        return [
          { value: 'Rise', label: 'Rise' },
          { value: 'Fall', label: 'Fall' }
        ];
      case 'DigitsOverUnder':
        return [
          { value: 'Over', label: 'Over' },
          { value: 'Under', label: 'Under' }
        ];
      default:
        return [];
    }
  }, []);

  // Get available strategy options for current trade type
  const availableStrategies = useMemo(() => {
    return getStrategyOptions(selectedUserTradeTypeForLoop);
  }, [selectedUserTradeTypeForLoop, getStrategyOptions]);

  // Pattern analysis functions
  const analyzePatterns = useCallback((ticks: Array<{price: number, digit: number, timestamp: number}>) => {
    if (ticks.length < 4) return { evenContinuous: 0, oddContinuous: 0, evenReversals: 0, oddReversals: 0 };

    // Analyze last 200 ticks (or all available if less than 200)
    const ticksToAnalyze = ticks.slice(-200);
    let evenContinuous = 0;
    let oddContinuous = 0;
    let evenReversals = 0;
    let oddReversals = 0;

    // Track continuous patterns (2+ consecutive same parity)
    let currentStreak = 1;
    let currentParity = ticksToAnalyze[0].digit % 2; // 0 for even, 1 for odd

    for (let i = 1; i < ticksToAnalyze.length; i++) {
      const tickParity = ticksToAnalyze[i].digit % 2;
      
      if (tickParity === currentParity) {
        currentStreak++;
      } else {
        // Streak ended, record if it was 2+ consecutive
        if (currentStreak >= 2) {
          if (currentParity === 0) {
            evenContinuous += currentStreak;
          } else {
            oddContinuous += currentStreak;
          }
        }
        
        // Count the reversal
        if (currentParity === 0) {
          evenReversals++;
        } else {
          oddReversals++;
        }
        
        // Start new streak
        currentStreak = 1;
        currentParity = tickParity;
      }
    }

    // Handle final streak
    if (currentStreak >= 2) {
      if (currentParity === 0) {
        evenContinuous += currentStreak;
      } else {
        oddContinuous += currentStreak;
      }
    }

    return { evenContinuous, oddContinuous, evenReversals, oddReversals };
  }, []);

  // Check for pattern-based trade triggers
  const checkPatternTriggers = useCallback((ticks: Array<{price: number, digit: number, timestamp: number}>) => {
    if (ticks.length < 4 || selectedUserTradeTypeForLoop !== 'DigitsEvenOdd' || !selectedStrategy) {
      return null;
    }

    const recentTicks = ticks.slice(-4); // Last 4 ticks for pattern detection
    const currentTick = recentTicks[recentTicks.length - 1];
    const previousTicks = recentTicks.slice(0, -1); // Last 3 ticks before current

    const currentDigitIsEven = currentTick.digit % 2 === 0;

    // Check if last 3 ticks are all opposite to current strategy
    const allPreviousOpposite = previousTicks.every(tick => {
      const tickIsEven = tick.digit % 2 === 0;
      if (selectedStrategy === 'Even') {
        return !tickIsEven; // All should be odd for Even strategy
      } else {
        return tickIsEven; // All should be even for Odd strategy
      }
    });

    if (allPreviousOpposite) {
      const contractType = selectedStrategy === 'Even' ? 'DIGITEVEN' : 'DIGITODD';
      return {
        shouldTrade: true,
        contractType,
        reasoning: `Pattern detected: 3 consecutive ${selectedStrategy === 'Even' ? 'odd' : 'even'} digits, trading ${selectedStrategy} on next tick`
      };
    }

    return null;
  }, [selectedUserTradeTypeForLoop, selectedStrategy]);

  // Handle pattern-triggered trades
  const handlePatternTriggeredTrade = useCallback(async (trigger: {shouldTrade: boolean, contractType: string, reasoning: string}) => {
    if (!userInfo?.id || !selectedDerivAccountType) {
      console.log('[VolatilityPage] Pattern trigger ignored: missing user info');
      return;
    }

    // Get the appropriate API token and account ID
    const userDerivApiToken = selectedDerivAccountType === 'demo' ? userInfo.derivDemoApiToken : userInfo.derivRealApiToken;
    const targetAccountId = selectedDerivAccountType === 'demo' ? userInfo.derivDemoAccountId : userInfo.derivRealAccountId;

    if (!userDerivApiToken || !targetAccountId) {
      console.log('[VolatilityPage] Pattern trigger ignored: missing API credentials');
      return;
    }

    try {
      console.log('[VolatilityPage] Executing pattern-triggered trade:', trigger);

      // Calculate stake per trade based on bulk trades setting
      const stakePerTrade = autoTradeTotalStake / numberOfBulkTrades;

      // Execute the pattern-triggered trade
      const results = await executeVolatilityAiTradeLoop(
        userDerivApiToken,
        targetAccountId,
        selectedDerivAccountType,
        userInfo.id,
        'DigitsEvenOdd',
        stakePerTrade,
        {
          executionMode,
          numberOfBulkTrades: 1, // Single trade for pattern trigger
          selectedInstrument: currentVolatilityInstrument,
          selectedStrategy: selectedStrategy,
          patternTrigger: trigger // Pass the trigger info
        }
      );

      if (results && results.length > 0) {
        toast({
          title: "Pattern Trade Executed",
          description: `${trigger.reasoning}`,
          duration: 5000
        });

        // Add to active trades for monitoring
        const newTrades = results.map(result => ({
          id: uuidv4(),
          instrument: currentVolatilityInstrument,
          tradeType: trigger.contractType === 'DIGITEVEN' ? 'Even' : 'Odd',
          entryPrice: result.tradeResponse?.entry_spot || currentStreamingPrice,
          buyPrice: stakePerTrade,
          profitLoss: undefined,
          derivContractType: trigger.contractType,
          userSelectedTradeType: 'DigitsEvenOdd' as UserTradeTypeValue,
          stake: stakePerTrade,
          durationSeconds: 5, // 5 ticks
          reasoning: trigger.reasoning,
          startTime: Date.now(),
          status: result.success ? 'active' : 'failed_placement',
          currentPrice: currentStreamingPrice,
          pnl: 0,
          error: result.error
        }));

        setActiveAutomatedTrades(prev => [...prev, ...newTrades]);
      }
    } catch (error) {
      console.error('[VolatilityPage] Pattern-triggered trade failed:', error);
      toast({
        title: "Pattern Trade Failed",
        description: `Failed to execute pattern trade: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive"
      });
    }
  }, [userInfo, selectedDerivAccountType, autoTradeTotalStake, numberOfBulkTrades, executionMode, currentVolatilityInstrument, selectedStrategy, currentStreamingPrice, toast]);

  // This component will contain all the trading logic
  // For now, return a simple loading message
  return (
    <div className="container mx-auto py-2 space-y-6">
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Activity className="h-8 w-8 text-primary mx-auto mb-4" />
          <h1 className="text-2xl font-bold mb-2">AI Volatility Index Trading</h1>
          <p className="text-muted-foreground">Loading trading interface...</p>
        </div>
      </div>
    </div>
  );
}
