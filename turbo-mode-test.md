# Turbo Mode Shared Price Point Fix - Test Plan

## 🎯 **Test Objective**
Verify that the Turbo execution mode for 1-second volatility indices correctly uses the same entry AND exit prices for all trades in a bulk execution.

## 🔧 **Fix Summary**
1. **Added shared price point enforcement** to `TradeDetails` interface
2. **Modified `placeTrade` function** to override market prices with shared price point in Turbo mode
3. **Enhanced trade execution logic** to pass shared price point and Turbo mode flag
4. **Added validation** to ensure shared price point is captured before execution
5. **Fixed decimal places support** for 1-second volatility indices API symbols

## 🧪 **Test Cases**

### **Test Case 1: Turbo Mode - 1-Second Volatility Index**
- **Instrument**: Volatility 10 (1s) Index
- **Execution Mode**: Turbo
- **Bulk Trades**: 5 trades
- **Trade Type**: Even/Odd
- **Expected Result**: All 5 trades have identical entry prices AND identical exit prices

### **Test Case 2: Turbo Mode - Standard Volatility Index**
- **Instrument**: Volatility 10 Index
- **Execution Mode**: Turbo
- **Bulk Trades**: 3 trades
- **Trade Type**: Over/Under
- **Expected Result**: All 3 trades have identical entry prices AND identical exit prices

### **Test Case 3: Safe Mode - 1-Second Volatility Index**
- **Instrument**: Volatility 25 (1s) Index
- **Execution Mode**: Safe
- **Bulk Trades**: 4 trades
- **Trade Type**: Even/Odd
- **Expected Result**: Trades have different entry prices (as intended for Safe mode)

## 🔍 **Verification Steps**

### **Step 1: Check Logs for Shared Price Point Capture**
Look for log entries:
```
[TradeAction/TickTiming] Turbo mode: Captured shared price point for 1HZ10V: 8731.93
```

### **Step 2: Check Logs for Price Point Enforcement**
Look for log entries:
```
[DerivService/placeTrade] TURBO MODE: Overriding market spot 8731.42 with shared price point 8731.93 for account VRTC13200397
```

### **Step 3: Check Trade Results Table**
Verify in the "Active AI Volatility Trades" table:
- **Entry Prices**: All identical (e.g., all $731.93)
- **Exit Prices**: All identical (e.g., all $731.93)

### **Step 4: Check for Validation Errors**
Ensure no errors like:
```
Invalid shared price point for Turbo mode trade: undefined on 1HZ10V
```

## ✅ **Success Criteria**

### **For Turbo Mode:**
- ✅ Shared price point is successfully captured
- ✅ All trades use the exact same entry price
- ✅ All trades use the exact same exit price
- ✅ No "Unhandled instrument" warnings for 1s indices
- ✅ Trades execute without validation errors

### **For Safe Mode:**
- ✅ Trades use different entry prices (normal behavior)
- ✅ No shared price point enforcement applied

## 🚨 **Known Issues Fixed**

### **Issue 1: Data Key Mismatch**
- **Problem**: AI couldn't find tick data for 1s indices
- **Fix**: Store data using API symbols as keys

### **Issue 2: Shared Price Point Not Enforced**
- **Problem**: placeTrade function ignored shared price point
- **Fix**: Added price override logic in placeTrade function

### **Issue 3: Decimal Places Warning**
- **Problem**: getInstrumentDecimalPlaces didn't support API symbols
- **Fix**: Added API symbol cases to the function

## 📊 **Expected Log Flow for Successful Turbo Mode**

```
[TradeAction/TickTiming] Turbo mode: Executing all 5 trades immediately with same entry/exit price
[TradeAction/TickTiming] Turbo mode: Captured shared price point for 1HZ10V: 8731.93
[TradeAction/SingleTrade] TURBO MODE VALIDATION: Using shared price point 8731.93 for 1HZ10V
[DerivService/placeTrade] TURBO MODE: Overriding market spot 8731.42 with shared price point 8731.93
[TradeAction/SingleTrade] TURBO MODE VALIDATION: Using shared price point 8731.93 for 1HZ10V
[DerivService/placeTrade] TURBO MODE: Overriding market spot 8731.68 with shared price point 8731.93
[TradeAction/SingleTrade] TURBO MODE VALIDATION: Using shared price point 8731.93 for 1HZ10V
[DerivService/placeTrade] TURBO MODE: Overriding market spot 8731.70 with shared price point 8731.93
```

## 🎯 **Final Verification**

After running the test, the trade results should show:
- **Entry Price**: All trades show 8731.93
- **Exit Price**: All trades show 8731.93
- **Status**: All trades execute successfully
- **Consistency**: Perfect price synchronization across all Turbo mode trades
