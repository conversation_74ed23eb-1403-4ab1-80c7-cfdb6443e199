2025-08-06T21:00:06.501Z [info] [DerivService/getDerivAccountBalance] WebSocket connection closed for accountId: CR8821305. Code: 1000, Reason: Balance successfully retrieved for CR8821305., WasClean: true. Duration: 43008ms.
2025-08-06T21:00:06.514Z [info] [TradeAction/Session] Starting AI session. User: ********, Account: VRTC13200397, Trade Type: DigitsEvenOdd, Total Stake: 5
2025-08-06T21:00:06.514Z [info] [TradeAction/Session] Execution Mode: turbo, Bulk Trades: 5, Selected Instrument: Volatility 50 (1s) Index
2025-08-06T21:00:06.514Z [info] [TradeAction/Session] Environment: Vercel Serverless, 1s Index: true
2025-08-06T21:00:06.514Z [info] [TradeAction/Session] CRITICAL FIX: Available volatility indices for data fetching: [
'Volatility 10 Index',
'Volatility 25 Index',
'Volatility 50 Index',
'Volatility 75 Index',
'Volatility 100 Index',
'Volatility 10 (1s) Index',
'Volatility 25 (1s) Index',
'Volatility 50 (1s) Index',
'Volatility 75 (1s) Index',
'Volatility 100 (1s) Index'
]
2025-08-06T21:00:06.514Z [info] [TradeAction/Session] Processing Volatility 10 Index -> API Symbol: R_10
2025-08-06T21:00:06.515Z [info] [TradeAction/Session] Fetching 25 ticks for Volatility 10 Index (1s index: false)
2025-08-06T21:00:06.605Z [info] [DerivService/getTicks] Authorizing with provided token.
2025-08-06T21:00:06.746Z [info] [DerivService/getTicks] Authorization successful/response received.
2025-08-06T21:00:07.106Z [info] [DerivService/getTicks] Sending ticks_history request (style:ticks): {"ticks_history":"R_10","adjust_start_time":1,"count":25,"end":"latest","style":"ticks"}
2025-08-06T21:00:07.188Z [info] [DerivService/getTicks] Closing WebSocket for R_10. Ticks received successfully
2025-08-06T21:00:07.189Z [info] [TradeAction/Session] Successfully stored data for Volatility 10 Index -> R_10: 25 ticks
2025-08-06T21:00:07.189Z [info] [TradeAction/Session] Processing Volatility 25 Index -> API Symbol: R_25
2025-08-06T21:00:07.189Z [info] [TradeAction/Session] Fetching 25 ticks for Volatility 25 Index (1s index: false)
2025-08-06T21:00:07.197Z [info] [DerivService/getTicks] WebSocket connection closed for R_10. Code: 1000, Reason: Ticks received successfully, Clean: true
2025-08-06T21:00:07.268Z [info] [DerivService/getTicks] Authorizing with provided token.
2025-08-06T21:00:07.428Z [info] [DerivService/getTicks] Authorization successful/response received.
2025-08-06T21:00:07.768Z [info] [DerivService/getTicks] Sending ticks_history request (style:ticks): {"ticks_history":"R_25","adjust_start_time":1,"count":25,"end":"latest","style":"ticks"}
2025-08-06T21:00:07.800Z [info] [DerivService/getTicks] Closing WebSocket for R_25. Ticks received successfully
2025-08-06T21:00:07.800Z [info] [TradeAction/Session] Successfully stored data for Volatility 25 Index -> R_25: 25 ticks
2025-08-06T21:00:07.800Z [info] [TradeAction/Session] Processing Volatility 50 Index -> API Symbol: R_50
2025-08-06T21:00:07.800Z [info] [TradeAction/Session] Fetching 25 ticks for Volatility 50 Index (1s index: false)
2025-08-06T21:00:07.811Z [info] [DerivService/getTicks] WebSocket connection closed for R_25. Code: 1000, Reason: Ticks received successfully, Clean: true
2025-08-06T21:00:07.893Z [info] [DerivService/getTicks] Authorizing with provided token.
2025-08-06T21:00:08.005Z [info] [DerivService/getTicks] Authorization successful/response received.
2025-08-06T21:00:08.394Z [info] [DerivService/getTicks] Sending ticks_history request (style:ticks): {"ticks_history":"R_50","adjust_start_time":1,"count":25,"end":"latest","style":"ticks"}
2025-08-06T21:00:08.417Z [info] [DerivService/getTicks] Closing WebSocket for R_50. Ticks received successfully
2025-08-06T21:00:08.418Z [info] [TradeAction/Session] Successfully stored data for Volatility 50 Index -> R_50: 25 ticks
2025-08-06T21:00:08.418Z [info] [TradeAction/Session] Processing Volatility 75 Index -> API Symbol: R_75
2025-08-06T21:00:08.418Z [info] [TradeAction/Session] Fetching 25 ticks for Volatility 75 Index (1s index: false)
2025-08-06T21:00:08.431Z [info] [DerivService/getTicks] WebSocket connection closed for R_50. Code: 1000, Reason: Ticks received successfully, Clean: true
2025-08-06T21:00:08.479Z [info] [DerivService/getTicks] Authorizing with provided token.
2025-08-06T21:00:08.617Z [info] [DerivService/getTicks] Authorization successful/response received.
2025-08-06T21:00:08.980Z [info] [DerivService/getTicks] Sending ticks_history request (style:ticks): {"ticks_history":"R_75","adjust_start_time":1,"count":25,"end":"latest","style":"ticks"}
2025-08-06T21:00:09.000Z [info] [DerivService/getTicks] Closing WebSocket for R_75. Ticks received successfully
2025-08-06T21:00:09.001Z [info] [TradeAction/Session] Successfully stored data for Volatility 75 Index -> R_75: 25 ticks
2025-08-06T21:00:09.001Z [info] [TradeAction/Session] Processing Volatility 100 Index -> API Symbol: R_100
2025-08-06T21:00:09.001Z [info] [TradeAction/Session] Fetching 25 ticks for Volatility 100 Index (1s index: false)
2025-08-06T21:00:09.010Z [info] [DerivService/getTicks] WebSocket connection closed for R_75. Code: 1000, Reason: Ticks received successfully, Clean: true
2025-08-06T21:00:09.051Z [info] [DerivService/getTicks] Authorizing with provided token.
2025-08-06T21:00:09.169Z [info] [DerivService/getTicks] Authorization successful/response received.
2025-08-06T21:00:09.551Z [info] [DerivService/getTicks] Sending ticks_history request (style:ticks): {"ticks_history":"R_100","adjust_start_time":1,"count":25,"end":"latest","style":"ticks"}
2025-08-06T21:00:09.575Z [info] [DerivService/getTicks] Closing WebSocket for R_100. Ticks received successfully
2025-08-06T21:00:09.575Z [info] [TradeAction/Session] Successfully stored data for Volatility 100 Index -> R_100: 25 ticks
2025-08-06T21:00:09.575Z [info] [TradeAction/Session] Processing Volatility 10 (1s) Index -> API Symbol: 1HZ10V
2025-08-06T21:00:09.575Z [info] [TradeAction/Session] Fetching 20 ticks for Volatility 10 (1s) Index (1s index: true)
2025-08-06T21:00:09.588Z [info] [DerivService/getTicks] WebSocket connection closed for R_100. Code: 1000, Reason: Ticks received successfully, Clean: true
2025-08-06T21:00:09.694Z [info] [DerivService/getTicks] Authorizing with provided token.
2025-08-06T21:00:09.835Z [info] [DerivService/getTicks] Authorization successful/response received.
2025-08-06T21:00:10.195Z [info] [DerivService/getTicks] Sending ticks_history request (style:ticks): {"ticks_history":"1HZ10V","adjust_start_time":1,"count":20,"end":"latest","style":"ticks"}
2025-08-06T21:00:10.270Z [info] [DerivService/getTicks] Closing WebSocket for 1HZ10V. Ticks received successfully
2025-08-06T21:00:10.270Z [info] [TradeAction/Session] Successfully stored data for Volatility 10 (1s) Index -> 1HZ10V: 20 ticks
2025-08-06T21:00:10.271Z [info] [TradeAction/Session] Processing Volatility 25 (1s) Index -> API Symbol: 1HZ25V
2025-08-06T21:00:10.271Z [info] [TradeAction/Session] Fetching 20 ticks for Volatility 25 (1s) Index (1s index: true)
2025-08-06T21:00:10.280Z [info] [DerivService/getTicks] WebSocket connection closed for 1HZ10V. Code: 1000, Reason: Ticks received successfully, Clean: true
2025-08-06T21:00:10.356Z [info] [DerivService/getTicks] Authorizing with provided token.
2025-08-06T21:00:10.492Z [info] [DerivService/getTicks] Authorization successful/response received.
2025-08-06T21:00:10.858Z [info] [DerivService/getTicks] Sending ticks_history request (style:ticks): {"ticks_history":"1HZ25V","adjust_start_time":1,"count":20,"end":"latest","style":"ticks"}
2025-08-06T21:00:10.876Z [info] [DerivService/getTicks] Closing WebSocket for 1HZ25V. Ticks received successfully
2025-08-06T21:00:10.877Z [info] [TradeAction/Session] Successfully stored data for Volatility 25 (1s) Index -> 1HZ25V: 20 ticks
2025-08-06T21:00:10.877Z [info] [TradeAction/Session] Processing Volatility 50 (1s) Index -> API Symbol: 1HZ50V
2025-08-06T21:00:10.877Z [info] [TradeAction/Session] Fetching 20 ticks for Volatility 50 (1s) Index (1s index: true)
2025-08-06T21:00:10.885Z [info] [DerivService/getTicks] WebSocket connection closed for 1HZ25V. Code: 1000, Reason: Ticks received successfully, Clean: true
2025-08-06T21:00:11.039Z [info] [DerivService/getTicks] Authorizing with provided token.
2025-08-06T21:00:11.210Z [info] [DerivService/getTicks] Authorization successful/response received.
2025-08-06T21:00:11.539Z [info] [DerivService/getTicks] Sending ticks_history request (style:ticks): {"ticks_history":"1HZ50V","adjust_start_time":1,"count":20,"end":"latest","style":"ticks"}
2025-08-06T21:00:11.557Z [info] [DerivService/getTicks] Closing WebSocket for 1HZ50V. Ticks received successfully
2025-08-06T21:00:11.557Z [info] [TradeAction/Session] Successfully stored data for Volatility 50 (1s) Index -> 1HZ50V: 20 ticks
2025-08-06T21:00:11.557Z [info] [TradeAction/Session] Processing Volatility 75 (1s) Index -> API Symbol: 1HZ75V
2025-08-06T21:00:11.557Z [info] [TradeAction/Session] Fetching 20 ticks for Volatility 75 (1s) Index (1s index: true)
2025-08-06T21:00:11.578Z [info] [DerivService/getTicks] WebSocket connection closed for 1HZ50V. Code: 1000, Reason: Ticks received successfully, Clean: true
2025-08-06T21:00:11.660Z [info] [DerivService/getTicks] Authorizing with provided token.
2025-08-06T21:00:11.758Z [info] [DerivService/getTicks] Authorization successful/response received.
2025-08-06T21:00:12.162Z [info] [DerivService/getTicks] Sending ticks_history request (style:ticks): {"ticks_history":"1HZ75V","adjust_start_time":1,"count":20,"end":"latest","style":"ticks"}
2025-08-06T21:00:12.270Z [info] [DerivService/getTicks] Closing WebSocket for 1HZ75V. Ticks received successfully
2025-08-06T21:00:12.271Z [info] [TradeAction/Session] Successfully stored data for Volatility 75 (1s) Index -> 1HZ75V: 20 ticks
2025-08-06T21:00:12.271Z [info] [TradeAction/Session] Processing Volatility 100 (1s) Index -> API Symbol: 1HZ100V
2025-08-06T21:00:12.271Z [info] [TradeAction/Session] Fetching 20 ticks for Volatility 100 (1s) Index (1s index: true)
2025-08-06T21:00:12.304Z [info] [DerivService/getTicks] WebSocket connection closed for 1HZ75V. Code: 1000, Reason: Ticks received successfully, Clean: true
2025-08-06T21:00:12.362Z [info] [DerivService/getTicks] Authorizing with provided token.
2025-08-06T21:00:12.519Z [info] [DerivService/getTicks] Authorization successful/response received.
2025-08-06T21:00:12.863Z [info] [DerivService/getTicks] Sending ticks_history request (style:ticks): {"ticks_history":"1HZ100V","adjust_start_time":1,"count":20,"end":"latest","style":"ticks"}
2025-08-06T21:00:12.893Z [info] [DerivService/getTicks] Closing WebSocket for 1HZ100V. Ticks received successfully
2025-08-06T21:00:12.893Z [info] [TradeAction/Session] Successfully stored data for Volatility 100 (1s) Index -> 1HZ100V: 20 ticks
2025-08-06T21:00:12.893Z [info] [TradeAction/Session] Available instruments with data: Volatility 10 Index, Volatility 25 Index, Volatility 50 Index, Volatility 75 Index, Volatility 100 Index, Volatility 10 (1s) Index, Volatility 25 (1s) Index, Volatility 50 (1s) Index, Volatility 75 (1s) Index, Volatility 100 (1s) Index
2025-08-06T21:00:12.893Z [info] [TradeAction/Session] Available API symbols with data: R_10, R_25, R_50, R_75, R_100, 1HZ10V, 1HZ25V, 1HZ50V, 1HZ75V, 1HZ100V
2025-08-06T21:00:12.893Z [info] [TradeAction/Session] Calling AI for session strategy. TradeType: DigitsEvenOdd, TotalStake: 5
2025-08-06T21:00:12.893Z [info] [TradeAction/Session] Using 40s timeout for Volatility 50 (1s) Index (1s index: true)
2025-08-06T21:00:12.893Z [info] [AI Session Flow] SINGLE INSTRUMENT TRADING - Target: Volatility 50 (1s) Index (Code: 1HZ50V)
2025-08-06T21:00:12.893Z [info] [AI Session Flow] User Settings - Trade Type: DigitsEvenOdd, Total Stake: 5, Execution Mode: turbo, Bulk Trades: 5, Account: demo, Strategy: Odd
2025-08-06T21:00:12.893Z [info] [AI Session Flow] SINGLE INSTRUMENT SESSION - Processing Volatility 50 (1s) Index (Code: 1HZ50V) with 5 trades, $1.00 per trade
2025-08-06T21:00:12.893Z [info] [AI Session Flow] Calling single trade decision 1/5 for Volatility 50 (1s) Index (Code: 1HZ50V) with stake $1.00
2025-08-06T21:00:12.893Z [info] [AI Single Flow] Input for 1HZ50V, type DigitsEvenOdd, stake 1
2025-08-06T21:00:12.893Z [info] [AI Single Flow/1HZ50V] Attempting Gemini generation (primary)
2025-08-06T21:00:12.893Z [info] [EnhancedAI] Attempting structured generation with Gemini (primary)
2025-08-06T21:00:12.927Z [info] [DerivService/getTicks] WebSocket connection closed for 1HZ100V. Code: 1000, Reason: Ticks received successfully, Clean: true
2025-08-06T21:00:14.205Z [info] [EnhancedAI] Gemini structured generation successful
2025-08-06T21:00:14.205Z [info] [AI Single Flow/1HZ50V] Enhanced AI generation successful (Gemini primary)
2025-08-06T21:00:14.211Z [info] [AI Session Flow] Trade 1 PROPOSED for Volatility 50 (1s) Index (Code: 1HZ50V). Stake: $1. Total allocated: $1.00
2025-08-06T21:00:14.211Z [info] [AI Session Flow] Calling single trade decision 2/5 for Volatility 50 (1s) Index (Code: 1HZ50V) with stake $1.00
2025-08-06T21:00:14.211Z [info] [AI Single Flow] Input for 1HZ50V, type DigitsEvenOdd, stake 1
2025-08-06T21:00:14.211Z [info] [AI Single Flow/1HZ50V] Attempting Gemini generation (primary)
2025-08-06T21:00:14.211Z [info] [EnhancedAI] Attempting structured generation with Gemini (primary)
2025-08-06T21:00:15.608Z [info] [EnhancedAI] Gemini structured generation successful
2025-08-06T21:00:15.608Z [info] [AI Single Flow/1HZ50V] Enhanced AI generation successful (Gemini primary)
2025-08-06T21:00:15.608Z [info] [AI Session Flow] Trade 2 PROPOSED for Volatility 50 (1s) Index (Code: 1HZ50V). Stake: $1. Total allocated: $2.00
2025-08-06T21:00:15.608Z [info] [AI Session Flow] Calling single trade decision 3/5 for Volatility 50 (1s) Index (Code: 1HZ50V) with stake $1.00
2025-08-06T21:00:15.608Z [info] [AI Single Flow] Input for 1HZ50V, type DigitsEvenOdd, stake 1
2025-08-06T21:00:15.608Z [info] [AI Single Flow/1HZ50V] Attempting Gemini generation (primary)
2025-08-06T21:00:15.609Z [info] [EnhancedAI] Attempting structured generation with Gemini (primary)
2025-08-06T21:00:16.723Z [info] [EnhancedAI] Gemini structured generation successful
2025-08-06T21:00:16.723Z [info] [AI Single Flow/1HZ50V] Enhanced AI generation successful (Gemini primary)
2025-08-06T21:00:16.723Z [info] [AI Session Flow] Trade 3 PROPOSED for Volatility 50 (1s) Index (Code: 1HZ50V). Stake: $1. Total allocated: $3.00
2025-08-06T21:00:16.723Z [info] [AI Session Flow] Calling single trade decision 4/5 for Volatility 50 (1s) Index (Code: 1HZ50V) with stake $1.00
2025-08-06T21:00:16.724Z [info] [AI Single Flow] Input for 1HZ50V, type DigitsEvenOdd, stake 1
2025-08-06T21:00:16.724Z [info] [AI Single Flow/1HZ50V] Attempting Gemini generation (primary)
2025-08-06T21:00:16.724Z [info] [EnhancedAI] Attempting structured generation with Gemini (primary)
2025-08-06T21:00:18.042Z [info] [EnhancedAI] Gemini structured generation successful
2025-08-06T21:00:18.042Z [info] [AI Single Flow/1HZ50V] Enhanced AI generation successful (Gemini primary)
2025-08-06T21:00:18.043Z [info] [AI Session Flow] Trade 4 PROPOSED for Volatility 50 (1s) Index (Code: 1HZ50V). Stake: $1. Total allocated: $4.00
2025-08-06T21:00:18.043Z [info] [AI Session Flow] Calling single trade decision 5/5 for Volatility 50 (1s) Index (Code: 1HZ50V) with stake $1.00
2025-08-06T21:00:18.043Z [info] [AI Single Flow] Input for 1HZ50V, type DigitsEvenOdd, stake 1
2025-08-06T21:00:18.043Z [info] [AI Single Flow/1HZ50V] Attempting Gemini generation (primary)
2025-08-06T21:00:18.043Z [info] [EnhancedAI] Attempting structured generation with Gemini (primary)
2025-08-06T21:00:19.244Z [info] [EnhancedAI] Gemini structured generation successful
2025-08-06T21:00:19.244Z [info] [AI Single Flow/1HZ50V] Enhanced AI generation successful (Gemini primary)
2025-08-06T21:00:19.244Z [info] [AI Session Flow] Trade 5 PROPOSED for Volatility 50 (1s) Index (Code: 1HZ50V). Stake: $1. Total allocated: $5.00
2025-08-06T21:00:19.245Z [info] [TradeAction/Session] AI Session Strategy received. Overall Reasoning: AI session for DigitsEvenOdd on Volatility 50 (1s) Index with total stake $5.00. Execution Mode: turbo, Bulk Trades: 5, Account: demo. Trade 1 on Volatility 50 (1s) Index: Based on the user selected strategy 'Odd' and recent digit patterns showing a slight bias towards odd numbers, a trade for DIGITODD is recommended. [User Settings: turbo mode, demo account, Strategy: Odd] (Stake: $1, Mode: turbo). Trade 2 on Volatility 50 (1s) Index: Based on the user's selected strategy 'Odd' and the recent digit analysis showing a slight bias towards odd digits (3, 9, 3, 3, 3, 5, 7), a trade for DigitsOdd is recommended. [User Settings: turbo mode, demo account, Strategy: Odd] (Stake: $1, Mode: turbo). Trade 3 on Volatility 50 (1s) Index: Based on the user's selected strategy (Odd) and the recent last digits showing a slight bias towards odd numbers, a trade for DigitsOdd is recommended. Using turbo mode for 1 tick duration. [User Settings: turbo mode, demo account, Strategy: Odd] (Stake: $1, Mode: turbo). Trade 4 on Volatility 50 (1s) Index: Based on the user's selected strategy (Odd) and the recent digit patterns, an Odd bet is recommended. [User Settings: turbo mode, demo account, Strategy: Odd] (Stake: $1, Mode: turbo). Trade 5 on Volatility 50 (1s) Index: Based on the user's selected strategy of 'Odd' and the recent digit analysis showing a slight bias towards odd digits, a trade for DigitsOdd is recommended. The duration is set to 1 tick due to the 'turbo' execution mode. [User Settings: turbo mode, demo account, Strategy: Odd] (Stake: $1, Mode: turbo). Total stake allocation limit nearly reached.
2025-08-06T21:00:19.245Z [info] [TradeAction/Session] AI proposes 5 trades.
2025-08-06T21:00:19.245Z [info] [TradeAction/TickTiming] Turbo mode: Executing all 5 trades immediately with same entry/exit price
2025-08-06T21:00:19.369Z [info] [DerivService/getTicks] Authorizing with provided token.
2025-08-06T21:00:19.469Z [info] [DerivService/getTicks] Authorization successful/response received.
2025-08-06T21:00:19.870Z [info] [DerivService/getTicks] Sending ticks_history request (style:ticks): {"ticks_history":"1HZ50V","adjust_start_time":1,"count":1,"end":"latest","style":"ticks"}
2025-08-06T21:00:19.886Z [info] [DerivService/getTicks] Closing WebSocket for 1HZ50V. Ticks received successfully
2025-08-06T21:00:19.887Z [info] [TradeAction/TickTiming] Turbo mode: Captured shared price point for 1HZ50V: 222817.7
2025-08-06T21:00:19.887Z [info] [TradeAction/SingleTrade] Processing AI proposed trade for: 1HZ50V (Deriv: 1HZ50V), Turbo Mode: true
2025-08-06T21:00:19.887Z [info] [TradeAction/SingleTrade] Turbo mode: Using shared price point for 1HZ50V: 222817.7
2025-08-06T21:00:19.887Z [info] [TradeAction/SingleTrade] Using strategy-based contract type: DIGITODD (strategy: Odd)
2025-08-06T21:00:19.887Z [info] [TradeAction/SingleTrade] TURBO MODE VALIDATION: Using shared price point 222817.7 for 1HZ50V
2025-08-06T21:00:19.887Z [info] [TradeAction/SingleTrade] Constructing TradeDetails for 1HZ50V: {
"symbol": "1HZ50V",
"contract_type": "DIGITODD",
"duration": 1,
"duration_unit": "t",
"amount": 1,
"currency": "USD",
"basis": "stake",
"token": "***REDACTED***",
"sharedPricePoint": 222817.7,
"isTurboMode": true
}
2025-08-06T21:00:19.887Z [info] [DerivService/placeTrade] Initiated for accountId: VRTC13200397, symbol: 1HZ50V at 2025-08-06T21:00:19.886Z
2025-08-06T21:00:19.903Z [info] [DerivService/getTicks] WebSocket connection closed for 1HZ50V. Code: 1000, Reason: Ticks received successfully, Clean: true
2025-08-06T21:00:19.942Z [info] [DerivService/placeTrade] WebSocket opened for accountId: VRTC13200397. Time to open: 56ms. Authorizing...
2025-08-06T21:00:19.942Z [info] [DerivService/placeTrade] Sending authorize request: {"authorize":"TOKEN_PRESENT"}
2025-08-06T21:00:20.032Z [info] [DerivService/placeTrade] Authorization successful. Token's current active account: VRTC13200397. Target account for trade: VRTC13200397.
2025-08-06T21:00:20.032Z [info] [DerivService/placeTrade] Session already active on target account VRTC13200397. Proceeding to proposal...
2025-08-06T21:00:20.032Z [info] [DerivService/placeTrade] Sending proposal request: {"proposal":1,"subscribe":1,"amount":1,"basis":"stake","contract_type":"DIGITODD","currency":"USD","symbol":"1HZ50V","duration":1,"duration_unit":"t","product_type":"basic"}
2025-08-06T21:00:20.093Z [info] [DerivService/placeTrade] Proposal received for account VRTC13200397. ID: b8b80fd3-5c6d-49f0-0d8f-164329aff7a1, Proposal Spot: 222817.7. Buying contract...
2025-08-06T21:00:20.093Z [info] [DerivService/placeTrade] Stored proposal subscription ID: b8b80fd3-5c6d-49f0-0d8f-164329aff7a1
2025-08-06T21:00:20.093Z [info] [DerivService/placeTrade] Sending buy request for account VRTC13200397: {"buy":"b8b80fd3-5c6d-49f0-0d8f-164329aff7a1","price":1}
2025-08-06T21:00:20.277Z [info] [DerivService/placeTrade] AccountID: VRTC13200397. Contract purchased successfully on account VRTC13200397: {"balance_after":9795.2,"buy_price":1,"contract_id":************,"longcode":"Win payout if the last digit of Volatility 50 (1s) Index is odd after 1 ticks.","payout":1.93,"purchase_time":**********,"shortcode":"DIGITODD_1HZ50V_1.93_**********_1T","start_time":**********,"transaction_id":************}. Duration: 391ms.
2025-08-06T21:00:20.277Z [info] [DerivService/placeTrade] Closing WebSocket for accountId: VRTC13200397. Original log: Contract purchased successfully on account VRTC13200397: {"balance_after":9795.2,"buy_price":1,"contract_id":************,"longcode":"Win payout if the last digit of Volatility 50 (1s) Index is odd after 1 ticks.","payout":1.93,"purchase_time":**********,"shortcode":"DIGITODD_1HZ50V_1.93_**********_1T","start_time":**********,"transaction_id":************}
2025-08-06T21:00:20.277Z [info] [DerivService/placeTrade] Forgetting subscription b8b80fd3-5c6d-49f0-0d8f-164329aff7a1 after buy message processed (Error: false).
2025-08-06T21:00:20.277Z [info] [TradeAction/SingleTrade] Deriv API placeTrade response for 1HZ50V: Contract ID ************
2025-08-06T21:00:20.287Z [info] [DerivService/placeTrade] WebSocket connection closed for accountId: VRTC13200397. Code: 1000, Reason: 'Contract purchased successfully on account VRTC13200397: {"balance_after":9795.2,"buy_price":1,"cont', WasClean: true. Duration: 400ms.
2025-08-06T21:00:20.361Z [info] [TradeAction/SingleTrade] Trade for 1HZ50V saved to DB. DB ID: c4034092-e30e-4433-8b67-671a0ffc711f
2025-08-06T21:00:20.361Z [info] [TradeAction/SingleTrade] Processing AI proposed trade for: 1HZ50V (Deriv: 1HZ50V), Turbo Mode: true
2025-08-06T21:00:20.361Z [info] [TradeAction/SingleTrade] Turbo mode: Using shared price point for 1HZ50V: 222817.7
2025-08-06T21:00:20.361Z [info] [TradeAction/SingleTrade] Using strategy-based contract type: DIGITODD (strategy: Odd)
2025-08-06T21:00:20.362Z [info] [TradeAction/SingleTrade] TURBO MODE VALIDATION: Using shared price point 222817.7 for 1HZ50V
2025-08-06T21:00:20.362Z [info] [TradeAction/SingleTrade] Constructing TradeDetails for 1HZ50V: {
"symbol": "1HZ50V",
"contract_type": "DIGITODD",
"duration": 1,
"duration_unit": "t",
"amount": 1,
"currency": "USD",
"basis": "stake",
"token": "***REDACTED***",
"sharedPricePoint": 222817.7,
"isTurboMode": true
}
2025-08-06T21:00:20.362Z [info] [DerivService/placeTrade] Initiated for accountId: VRTC13200397, symbol: 1HZ50V at 2025-08-06T21:00:20.361Z
2025-08-06T21:00:20.473Z [info] [DerivService/placeTrade] WebSocket opened for accountId: VRTC13200397. Time to open: 112ms. Authorizing...
2025-08-06T21:00:20.473Z [info] [DerivService/placeTrade] Sending authorize request: {"authorize":"TOKEN_PRESENT"}
2025-08-06T21:00:20.567Z [info] [DerivService/placeTrade] Authorization successful. Token's current active account: VRTC13200397. Target account for trade: VRTC13200397.
2025-08-06T21:00:20.567Z [info] [DerivService/placeTrade] Session already active on target account VRTC13200397. Proceeding to proposal...
2025-08-06T21:00:20.567Z [info] [DerivService/placeTrade] Sending proposal request: {"proposal":1,"subscribe":1,"amount":1,"basis":"stake","contract_type":"DIGITODD","currency":"USD","symbol":"1HZ50V","duration":1,"duration_unit":"t","product_type":"basic"}
2025-08-06T21:00:20.624Z [info] [DerivService/placeTrade] Proposal received for account VRTC13200397. ID: 41aad852-42ac-c365-3df8-4f758a304f38, Proposal Spot: 222805.92. Buying contract...
2025-08-06T21:00:20.624Z [info] [DerivService/placeTrade] Stored proposal subscription ID: 41aad852-42ac-c365-3df8-4f758a304f38
2025-08-06T21:00:20.624Z [info] [DerivService/placeTrade] Sending buy request for account VRTC13200397: {"buy":"41aad852-42ac-c365-3df8-4f758a304f38","price":1}
2025-08-06T21:00:20.723Z [info] [DerivService/placeTrade] AccountID: VRTC13200397. Contract purchased successfully on account VRTC13200397: {"balance_after":9794.2,"buy_price":1,"contract_id":************,"longcode":"Win payout if the last digit of Volatility 50 (1s) Index is odd after 1 ticks.","payout":1.93,"purchase_time":**********,"shortcode":"DIGITODD_1HZ50V_1.93_**********_1T","start_time":**********,"transaction_id":************}. Duration: 359ms.
2025-08-06T21:00:20.723Z [info] [DerivService/placeTrade] Closing WebSocket for accountId: VRTC13200397. Original log: Contract purchased successfully on account VRTC13200397: {"balance_after":9794.2,"buy_price":1,"contract_id":************,"longcode":"Win payout if the last digit of Volatility 50 (1s) Index is odd after 1 ticks.","payout":1.93,"purchase_time":**********,"shortcode":"DIGITODD_1HZ50V_1.93_**********_1T","start_time":**********,"transaction_id":************}
2025-08-06T21:00:20.723Z [info] [DerivService/placeTrade] Forgetting subscription 41aad852-42ac-c365-3df8-4f758a304f38 after buy message processed (Error: false).
2025-08-06T21:00:20.723Z [info] [TradeAction/SingleTrade] Deriv API placeTrade response for 1HZ50V: Contract ID ************
2025-08-06T21:00:20.732Z [info] [TradeAction/SingleTrade] Trade for 1HZ50V saved to DB. DB ID: 7d2fcec6-3ed7-4248-b21f-d554aa67db5e
2025-08-06T21:00:20.732Z [info] [TradeAction/SingleTrade] Processing AI proposed trade for: 1HZ50V (Deriv: 1HZ50V), Turbo Mode: true
2025-08-06T21:00:20.732Z [info] [TradeAction/SingleTrade] Turbo mode: Using shared price point for 1HZ50V: 222817.7
2025-08-06T21:00:20.732Z [info] [TradeAction/SingleTrade] Using strategy-based contract type: DIGITODD (strategy: Odd)
2025-08-06T21:00:20.732Z [info] [TradeAction/SingleTrade] TURBO MODE VALIDATION: Using shared price point 222817.7 for 1HZ50V
2025-08-06T21:00:20.732Z [info] [TradeAction/SingleTrade] Constructing TradeDetails for 1HZ50V: {
"symbol": "1HZ50V",
"contract_type": "DIGITODD",
"duration": 1,
"duration_unit": "t",
"amount": 1,
"currency": "USD",
"basis": "stake",
"token": "***REDACTED***",
"sharedPricePoint": 222817.7,
"isTurboMode": true
}
2025-08-06T21:00:20.732Z [info] [DerivService/placeTrade] Initiated for accountId: VRTC13200397, symbol: 1HZ50V at 2025-08-06T21:00:20.731Z
2025-08-06T21:00:20.739Z [info] [DerivService/placeTrade] WebSocket connection closed for accountId: VRTC13200397. Code: 1000, Reason: 'Contract purchased successfully on account VRTC13200397: {"balance_after":9794.2,"buy_price":1,"cont', WasClean: true. Duration: 378ms.
2025-08-06T21:00:20.785Z [info] [DerivService/placeTrade] WebSocket opened for accountId: VRTC13200397. Time to open: 53ms. Authorizing...
2025-08-06T21:00:20.785Z [info] [DerivService/placeTrade] Sending authorize request: {"authorize":"TOKEN_PRESENT"}
2025-08-06T21:00:20.886Z [info] [DerivService/placeTrade] Authorization successful. Token's current active account: VRTC13200397. Target account for trade: VRTC13200397.
2025-08-06T21:00:20.886Z [info] [DerivService/placeTrade] Session already active on target account VRTC13200397. Proceeding to proposal...
2025-08-06T21:00:20.886Z [info] [DerivService/placeTrade] Sending proposal request: {"proposal":1,"subscribe":1,"amount":1,"basis":"stake","contract_type":"DIGITODD","currency":"USD","symbol":"1HZ50V","duration":1,"duration_unit":"t","product_type":"basic"}
2025-08-06T21:00:20.934Z [info] [DerivService/placeTrade] Proposal received for account VRTC13200397. ID: 1c067fb2-2e6d-79df-33aa-bb4692b3f9c5, Proposal Spot: 222805.92. Buying contract...
2025-08-06T21:00:20.934Z [info] [DerivService/placeTrade] Stored proposal subscription ID: 1c067fb2-2e6d-79df-33aa-bb4692b3f9c5
2025-08-06T21:00:20.934Z [info] [DerivService/placeTrade] Sending buy request for account VRTC13200397: {"buy":"1c067fb2-2e6d-79df-33aa-bb4692b3f9c5","price":1}
2025-08-06T21:00:21.020Z [info] [DerivService/placeTrade] AccountID: VRTC13200397. Contract purchased successfully on account VRTC13200397: {"balance_after":9793.2,"buy_price":1,"contract_id":************,"longcode":"Win payout if the last digit of Volatility 50 (1s) Index is odd after 1 ticks.","payout":1.93,"purchase_time":**********,"shortcode":"DIGITODD_1HZ50V_1.93_**********_1T","start_time":**********,"transaction_id":************}. Duration: 289ms.
2025-08-06T21:00:21.020Z [info] [DerivService/placeTrade] Closing WebSocket for accountId: VRTC13200397. Original log: Contract purchased successfully on account VRTC13200397: {"balance_after":9793.2,"buy_price":1,"contract_id":************,"longcode":"Win payout if the last digit of Volatility 50 (1s) Index is odd after 1 ticks.","payout":1.93,"purchase_time":**********,"shortcode":"DIGITODD_1HZ50V_1.93_**********_1T","start_time":**********,"transaction_id":************}
2025-08-06T21:00:21.020Z [info] [DerivService/placeTrade] Forgetting subscription 1c067fb2-2e6d-79df-33aa-bb4692b3f9c5 after buy message processed (Error: false).
2025-08-06T21:00:21.021Z [info] [TradeAction/SingleTrade] Deriv API placeTrade response for 1HZ50V: Contract ID ************
2025-08-06T21:00:21.031Z [info] [DerivService/placeTrade] WebSocket connection closed for accountId: VRTC13200397. Code: 1000, Reason: 'Contract purchased successfully on account VRTC13200397: {"balance_after":9793.2,"buy_price":1,"cont', WasClean: true. Duration: 299ms.
2025-08-06T21:00:21.031Z [info] [TradeAction/SingleTrade] Trade for 1HZ50V saved to DB. DB ID: a77f8339-21be-4546-b17d-4205b7aebdf9
2025-08-06T21:00:21.033Z [info] [TradeAction/SingleTrade] Processing AI proposed trade for: 1HZ50V (Deriv: 1HZ50V), Turbo Mode: true
2025-08-06T21:00:21.033Z [info] [TradeAction/SingleTrade] Turbo mode: Using shared price point for 1HZ50V: 222817.7
2025-08-06T21:00:21.033Z [info] [TradeAction/SingleTrade] Using strategy-based contract type: DIGITODD (strategy: Odd)
2025-08-06T21:00:21.033Z [info] [TradeAction/SingleTrade] TURBO MODE VALIDATION: Using shared price point 222817.7 for 1HZ50V
2025-08-06T21:00:21.033Z [info] [TradeAction/SingleTrade] Constructing TradeDetails for 1HZ50V: {
"symbol": "1HZ50V",
"contract_type": "DIGITODD",
"duration": 1,
"duration_unit": "t",
"amount": 1,
"currency": "USD",
"basis": "stake",
"token": "***REDACTED***",
"sharedPricePoint": 222817.7,
"isTurboMode": true
}
2025-08-06T21:00:21.033Z [info] [DerivService/placeTrade] Initiated for accountId: VRTC13200397, symbol: 1HZ50V at 2025-08-06T21:00:21.031Z
2025-08-06T21:00:21.149Z [info] [DerivService/placeTrade] WebSocket opened for accountId: VRTC13200397. Time to open: 117ms. Authorizing...
2025-08-06T21:00:21.149Z [info] [DerivService/placeTrade] Sending authorize request: {"authorize":"TOKEN_PRESENT"}
2025-08-06T21:00:21.255Z [info] [DerivService/placeTrade] Authorization successful. Token's current active account: VRTC13200397. Target account for trade: VRTC13200397.
2025-08-06T21:00:21.255Z [info] [DerivService/placeTrade] Session already active on target account VRTC13200397. Proceeding to proposal...
2025-08-06T21:00:21.255Z [info] [DerivService/placeTrade] Sending proposal request: {"proposal":1,"subscribe":1,"amount":1,"basis":"stake","contract_type":"DIGITODD","currency":"USD","symbol":"1HZ50V","duration":1,"duration_unit":"t","product_type":"basic"}
2025-08-06T21:00:21.331Z [info] [DerivService/placeTrade] Proposal received for account VRTC13200397. ID: 502c0c1b-ddbe-4e17-ef8e-2b192340e872, Proposal Spot: 222810.7. Buying contract...
2025-08-06T21:00:21.331Z [info] [DerivService/placeTrade] Stored proposal subscription ID: 502c0c1b-ddbe-4e17-ef8e-2b192340e872
2025-08-06T21:00:21.331Z [info] [DerivService/placeTrade] Sending buy request for account VRTC13200397: {"buy":"502c0c1b-ddbe-4e17-ef8e-2b192340e872","price":1}
2025-08-06T21:00:21.439Z [info] [DerivService/placeTrade] AccountID: VRTC13200397. Contract purchased successfully on account VRTC13200397: {"balance_after":9792.2,"buy_price":1,"contract_id":************,"longcode":"Win payout if the last digit of Volatility 50 (1s) Index is odd after 1 ticks.","payout":1.93,"purchase_time":**********,"shortcode":"DIGITODD_1HZ50V_1.93_**********_1T","start_time":**********,"transaction_id":************}. Duration: 407ms.
2025-08-06T21:00:21.439Z [info] [DerivService/placeTrade] Closing WebSocket for accountId: VRTC13200397. Original log: Contract purchased successfully on account VRTC13200397: {"balance_after":9792.2,"buy_price":1,"contract_id":************,"longcode":"Win payout if the last digit of Volatility 50 (1s) Index is odd after 1 ticks.","payout":1.93,"purchase_time":**********,"shortcode":"DIGITODD_1HZ50V_1.93_**********_1T","start_time":**********,"transaction_id":************}
2025-08-06T21:00:21.439Z [info] [DerivService/placeTrade] Forgetting subscription 502c0c1b-ddbe-4e17-ef8e-2b192340e872 after buy message processed (Error: false).
2025-08-06T21:00:21.439Z [info] [TradeAction/SingleTrade] Deriv API placeTrade response for 1HZ50V: Contract ID ************
2025-08-06T21:00:21.451Z [info] [TradeAction/SingleTrade] Trade for 1HZ50V saved to DB. DB ID: fe4ca711-6a24-4332-a61a-bc3e1b64a742
2025-08-06T21:00:21.451Z [info] [TradeAction/SingleTrade] Processing AI proposed trade for: 1HZ50V (Deriv: 1HZ50V), Turbo Mode: true
2025-08-06T21:00:21.451Z [info] [TradeAction/SingleTrade] Turbo mode: Using shared price point for 1HZ50V: 222817.7
2025-08-06T21:00:21.451Z [info] [TradeAction/SingleTrade] Using strategy-based contract type: DIGITODD (strategy: Odd)
2025-08-06T21:00:21.451Z [info] [TradeAction/SingleTrade] TURBO MODE VALIDATION: Using shared price point 222817.7 for 1HZ50V
2025-08-06T21:00:21.451Z [info] [TradeAction/SingleTrade] Constructing TradeDetails for 1HZ50V: {
"symbol": "1HZ50V",
"contract_type": "DIGITODD",
"duration": 1,
"duration_unit": "t",
"amount": 1,
"currency": "USD",
"basis": "stake",
"token": "***REDACTED***",
"sharedPricePoint": 222817.7,
"isTurboMode": true
}
2025-08-06T21:00:21.451Z [info] [DerivService/placeTrade] Initiated for accountId: VRTC13200397, symbol: 1HZ50V at 2025-08-06T21:00:21.450Z
2025-08-06T21:00:21.454Z [info] [DerivService/placeTrade] WebSocket connection closed for accountId: VRTC13200397. Code: 1000, Reason: 'Contract purchased successfully on account VRTC13200397: {"balance_after":9792.2,"buy_price":1,"cont', WasClean: true. Duration: 422ms.
2025-08-06T21:00:21.541Z [info] [DerivService/placeTrade] WebSocket opened for accountId: VRTC13200397. Time to open: 91ms. Authorizing...
2025-08-06T21:00:21.541Z [info] [DerivService/placeTrade] Sending authorize request: {"authorize":"TOKEN_PRESENT"}
2025-08-06T21:00:21.696Z [info] [DerivService/placeTrade] Authorization successful. Token's current active account: VRTC13200397. Target account for trade: VRTC13200397.
2025-08-06T21:00:21.696Z [info] [DerivService/placeTrade] Session already active on target account VRTC13200397. Proceeding to proposal...
2025-08-06T21:00:21.696Z [info] [DerivService/placeTrade] Sending proposal request: {"proposal":1,"subscribe":1,"amount":1,"basis":"stake","contract_type":"DIGITODD","currency":"USD","symbol":"1HZ50V","duration":1,"duration_unit":"t","product_type":"basic"}
2025-08-06T21:00:21.760Z [info] [DerivService/placeTrade] Proposal received for account VRTC13200397. ID: 2df2e47e-c186-05ea-c72b-51b825bca88f, Proposal Spot: 222810.7. Buying contract...
2025-08-06T21:00:21.760Z [info] [DerivService/placeTrade] Stored proposal subscription ID: 2df2e47e-c186-05ea-c72b-51b825bca88f
2025-08-06T21:00:21.760Z [info] [DerivService/placeTrade] Sending buy request for account VRTC13200397: {"buy":"2df2e47e-c186-05ea-c72b-51b825bca88f","price":1}
2025-08-06T21:00:21.851Z [info] [DerivService/placeTrade] AccountID: VRTC13200397. Contract purchased successfully on account VRTC13200397: {"balance_after":9791.2,"buy_price":1,"contract_id":************,"longcode":"Win payout if the last digit of Volatility 50 (1s) Index is odd after 1 ticks.","payout":1.93,"purchase_time":**********,"shortcode":"DIGITODD_1HZ50V_1.93_**********_1T","start_time":**********,"transaction_id":************}. Duration: 401ms.
2025-08-06T21:00:21.851Z [info] [DerivService/placeTrade] Closing WebSocket for accountId: VRTC13200397. Original log: Contract purchased successfully on account VRTC13200397: {"balance_after":9791.2,"buy_price":1,"contract_id":************,"longcode":"Win payout if the last digit of Volatility 50 (1s) Index is odd after 1 ticks.","payout":1.93,"purchase_time":**********,"shortcode":"DIGITODD_1HZ50V_1.93_**********_1T","start_time":**********,"transaction_id":************}
2025-08-06T21:00:21.851Z [info] [DerivService/placeTrade] Forgetting subscription 2df2e47e-c186-05ea-c72b-51b825bca88f after buy message processed (Error: false).
2025-08-06T21:00:21.851Z [info] [TradeAction/SingleTrade] Deriv API placeTrade response for 1HZ50V: Contract ID ************
2025-08-06T21:00:21.862Z [info] [TradeAction/SingleTrade] Trade for 1HZ50V saved to DB. DB ID: d68c8c1c-efb1-4c5b-a5d5-0baacbf83484
2025-08-06T21:00:21.862Z [info] [TradeAction/Session] Finished Volatility AI session. Total results processed: 5