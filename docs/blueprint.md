# **App Name**: DerivAI Lite

## Core Features:

- Real-Time Dashboard: Display live candlestick charts for EUR/USD, GBP/USD, and BTC/USD using TradingView Lite, with animated trade execution buttons and real-time balance updates in a mobile-optimized layout.
- AI Trade Recommendations: AI analyses market sentiment from forex-related news using FinBERT, LSTM for price trend analysis, and an ensemble model to combine signals to suggest optimal durations for trades as a trading tool. The models automatically use the real market data to make uniformed decisions whether they are using a real/ demo account
- Trading Mode Selection: Allow users to switch between conservative, balanced, and aggressive trading modes to adjust risk and reward profiles. The AI should explain its reasoning in simple terms (e.g., "Recommending CALL because RSI is oversold and volatility is increasing"). Implement a 'paper trading' mode that works alongside live trading

## Style Guidelines:

- Primary color: Dark blue (#1A202C) for a professional and sophisticated feel.
- Secondary color: Light gray (#EDF2F7) for backgrounds and subtle contrasts.
- Accent: Teal (#4DC0B5) for interactive elements and highlights, providing a sense of trustworthiness.
- Use a clean, grid-based layout for a structured and easy-to-navigate interface.
- Use a set of consistent and modern icons to represent different trading functions and data points.
- Subtle transitions and animations for trade executions and data updates to enhance user experience.