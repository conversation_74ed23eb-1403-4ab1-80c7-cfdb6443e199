import type {NextConfig} from 'next';

const nextConfig: NextConfig = {
  /* config options here */
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'picsum.photos',
        port: '',
        pathname: '/**',
      },
      // Add Google User Content for profile pictures
      {
        protocol: 'https',
        hostname: 'lh3.googleusercontent.com',
        port: '',
        pathname: '/**',
      }
    ],
  },
  // Ensure NEXT_PUBLIC_ environment variables are available client-side
  // This is usually handled automatically by Next.js for variables prefixed with NEXT_PUBLIC_
  // No explicit env key needed here unless there's a specific non-standard requirement.
  webpack: (config) => {
    config.module.rules.push({
      test: /\.handlebars$/,
      loader: 'handlebars-loader',
    });

    config.resolve.alias = {
      ...config.resolve.alias,
    };
    // Important: return the modified config
    return config;
  },
};

export default nextConfig;
