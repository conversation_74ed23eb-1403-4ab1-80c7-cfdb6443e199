{"name": "nextn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 9002", "genkit:dev": "genkit start -- tsx src/ai/dev.ts", "genkit:watch": "genkit start -- tsx --watch src/ai/dev.ts", "build": "npx prisma generate && next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit"}, "dependencies": {"@genkit-ai/googleai": "^1.6.2", "@genkit-ai/next": "^1.6.2", "@hookform/resolvers": "^4.1.3", "@huggingface/inference": "^2.8.1", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.7.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack-query-firebase/react": "^1.0.5", "@tanstack/react-query": "^5.66.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "firebase": "^11.7.1", "firebase-admin": "^13.4.0", "genkit": "^1.6.2", "lucide-react": "^0.475.0", "next": "^15.3.3", "next-auth": "^4.24.11", "nodemailer": "^6.10.1", "openai": "^5.7.0", "patch-package": "^8.0.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.2", "react-icons": "^5.2.1", "recharts": "^2.15.1", "smtp-server": "^3.13.6", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "technicalindicators": "^3.1.0", "uuid": "^10.0.0", "ws": "^8.18.2", "zod": "^3.24.2"}, "devDependencies": {"@opentelemetry/exporter-jaeger": "^1.0.0", "@types/bcryptjs": "^2.4.6", "@types/chrome": "^0.0.322", "@types/node": "^20.19.1", "@types/nodemailer": "^6.4.17", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "@types/ws": "^8.5.10", "genkit-cli": "^1.6.1", "handlebars-loader": "^1.7.3", "postcss": "^8", "prisma": "^6.7.0", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5"}, "optionalDependencies": {"bufferutil": "^4.0.8", "utf-8-validate": "^6.0.3"}}